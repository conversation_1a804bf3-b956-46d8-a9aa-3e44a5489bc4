<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('csv_transformations', function (Blueprint $table) {
            $table->string('config_file')->nullable()->after('destination_file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('csv_transformations', function (Blueprint $table) {
            $table->dropColumn('config_file');
        });
    }
};
