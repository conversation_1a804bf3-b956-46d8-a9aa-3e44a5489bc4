<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('csv_data_sources', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['database', 'file', 'api', 'cloud']);
            $table->enum('status', ['connected', 'disconnected', 'error']);
            $table->text('description')->nullable();
            $table->json('configuration');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('last_sync_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('csv_data_sources');
    }
};
