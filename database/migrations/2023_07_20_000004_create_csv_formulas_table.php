<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('csv_formulas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('template_id')->constrained('csv_templates')->onDelete('cascade');
            $table->string('column_id');
            $table->string('type');
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('parameters')->nullable();
            $table->integer('order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('csv_formulas');
    }
};
