<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CsvTransformation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'status',
        'source_file',
        'destination_file',
        'config_file',
        'user_id',
        'template_id',
        'records_processed',
        'file_size',
        'started_at',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the transformation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the template that was used for the transformation.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(CsvTemplate::class, 'template_id');
    }

    /**
     * Get the notifications for the transformation.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }
}
