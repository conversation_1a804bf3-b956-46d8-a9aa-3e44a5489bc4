<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CsvFormula extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'template_id',
        'column_id',
        'type',
        'name',
        'description',
        'parameters',
        'order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parameters' => 'json',
    ];

    /**
     * Get the template that owns the formula.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(CsvTemplate::class, 'template_id');
    }
}
