<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CsvTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'user_id',
        'configuration',
        'last_used_at',
        'usage_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'configuration' => 'json',
        'last_used_at' => 'datetime',
    ];

    /**
     * Get the user that owns the template.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transformations that use this template.
     */
    public function transformations(): HasMany
    {
        return $this->hasMany(CsvTransformation::class, 'template_id');
    }

    /**
     * Get the formulas for this template.
     */
    public function formulas(): Has<PERSON>any
    {
        return $this->hasMany(CsvFormula::class, 'template_id');
    }
}
