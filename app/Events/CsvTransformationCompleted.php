<?php

namespace App\Events;

use App\Models\CsvTransformation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CsvTransformationCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $transformation;

    /**
     * Create a new event instance.
     */
    public function __construct(CsvTransformation $transformation)
    {
        $this->transformation = $transformation;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->transformation->user_id),
        ];
    }
    
    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->transformation->id,
            'name' => $this->transformation->name,
            'status' => 'completed',
            'records_processed' => $this->transformation->records_processed,
            'file_size' => $this->transformation->file_size,
            'completed_at' => $this->transformation->completed_at->toIso8601String(),
            'destination_file' => $this->transformation->destination_file,
        ];
    }
}
