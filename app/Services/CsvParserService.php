<?php

namespace App\Services;

class CsvParserService
{
    /**
     * Parse a CSV file into an array of rows.
     *
     * @param string $filePath
     * @return array
     */
    public function parseFile(string $filePath): array
    {
        $rows = [];
        
        if (($handle = fopen($filePath, "r")) !== false) {
            while (($data = fgetcsv($handle, 1000, ",")) !== false) {
                $rows[] = $data;
            }
            fclose($handle);
        }
        
        return $rows;
    }
    
    /**
     * Parse a CSV string into an array of rows.
     *
     * @param string $csvString
     * @return array
     */
    public function parseString(string $csvString): array
    {
        $rows = [];
        $lines = explode("\n", $csvString);
        
        foreach ($lines as $line) {
            if (empty(trim($line))) {
                continue;
            }
            
            $row = [];
            $inQuotes = false;
            $currentValue = '';
            
            for ($i = 0; $i < strlen($line); $i++) {
                $char = $line[$i];
                $nextChar = $i < strlen($line) - 1 ? $line[$i + 1] : null;
                
                if ($char === '"' && $inQuotes && $nextChar === '"') {
                    // <PERSON>le escaped quotes
                    $currentValue .= '"';
                    $i++;
                } elseif ($char === '"') {
                    // Toggle quotes mode
                    $inQuotes = !$inQuotes;
                } elseif ($char === ',' && !$inQuotes) {
                    // End of field
                    $row[] = $currentValue;
                    $currentValue = '';
                } else {
                    // Normal character
                    $currentValue .= $char;
                }
            }
            
            // Don't forget the last field
            $row[] = $currentValue;
            $rows[] = $row;
        }
        
        return $rows;
    }
    
    /**
     * Create a structured CSV data array from parsed rows.
     *
     * @param array $parsedData
     * @param string $fileName
     * @return array
     */
    public function createCsvData(array $parsedData, string $fileName): array
    {
        if (empty($parsedData)) {
            return [
                'columns' => [],
                'rows' => [],
                'fileName' => $fileName
            ];
        }
        
        $headerRow = $parsedData[0];
        $columns = [];
        
        foreach ($headerRow as $index => $name) {
            $columns[] = [
                'id' => "col-{$index}",
                'originalName' => $name,
                'mappedName' => $name,
                'formulas' => []
            ];
        }
        
        $rows = [];
        
        for ($rowIndex = 1; $rowIndex < count($parsedData); $rowIndex++) {
            $rowData = $parsedData[$rowIndex];
            $data = [];
            
            foreach ($columns as $colIndex => $column) {
                $data[$column['id']] = $colIndex < count($rowData) ? $rowData[$colIndex] : '';
            }
            
            $rows[] = [
                'id' => "row-{$rowIndex}",
                'data' => $data,
                'included' => true
            ];
        }
        
        return [
            'columns' => $columns,
            'rows' => $rows,
            'fileName' => $fileName
        ];
    }
}
