<?php

namespace App\Http\Controllers;

use App\Models\CsvDataSource;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CsvDataSourceController extends Controller
{
    /**
     * Display a listing of the data sources.
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        $dataSources = CsvDataSource::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        return Inertia::render('csv/data-sources', [
            'dataSources' => $dataSources,
        ]);
    }
    
    /**
     * Store a newly created data source in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:database,file,api,cloud',
            'description' => 'nullable|string',
            'configuration' => 'required|json',
        ]);
        
        try {
            $dataSource = CsvDataSource::create([
                'name' => $request->input('name'),
                'type' => $request->input('type'),
                'status' => 'connected',
                'description' => $request->input('description'),
                'configuration' => json_decode($request->input('configuration'), true),
                'user_id' => auth()->id(),
                'last_sync_at' => now(),
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Data source created successfully',
                'dataSource' => $dataSource,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create data source: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Display the specified data source.
     *
     * @param CsvDataSource $dataSource
     * @return \Inertia\Response
     */
    public function show(CsvDataSource $dataSource)
    {
        // Check if the user owns this data source
        if ($dataSource->user_id !== auth()->id()) {
            abort(403);
        }
        
        return Inertia::render('csv/data-source-details', [
            'dataSource' => $dataSource,
        ]);
    }
    
    /**
     * Update the specified data source in storage.
     *
     * @param Request $request
     * @param CsvDataSource $dataSource
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, CsvDataSource $dataSource)
    {
        // Check if the user owns this data source
        if ($dataSource->user_id !== auth()->id()) {
            abort(403);
        }
        
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:database,file,api,cloud',
            'status' => 'required|in:connected,disconnected,error',
            'description' => 'nullable|string',
            'configuration' => 'required|json',
        ]);
        
        try {
            $dataSource->update([
                'name' => $request->input('name'),
                'type' => $request->input('type'),
                'status' => $request->input('status'),
                'description' => $request->input('description'),
                'configuration' => json_decode($request->input('configuration'), true),
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Data source updated successfully',
                'dataSource' => $dataSource,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update data source: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Remove the specified data source from storage.
     *
     * @param CsvDataSource $dataSource
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(CsvDataSource $dataSource)
    {
        // Check if the user owns this data source
        if ($dataSource->user_id !== auth()->id()) {
            abort(403);
        }
        
        try {
            $dataSource->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Data source deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete data source: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Test the connection to the data source.
     *
     * @param CsvDataSource $dataSource
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection(CsvDataSource $dataSource)
    {
        // Check if the user owns this data source
        if ($dataSource->user_id !== auth()->id()) {
            abort(403);
        }
        
        try {
            // In a real implementation, this would test the actual connection
            // For now, we'll just simulate a successful connection
            $dataSource->update([
                'status' => 'connected',
                'last_sync_at' => now(),
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Connection successful',
            ]);
        } catch (\Exception $e) {
            $dataSource->update([
                'status' => 'error',
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage(),
            ], 500);
        }
    }
}
