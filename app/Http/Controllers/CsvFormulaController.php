<?php

namespace App\Http\Controllers;

use App\Services\FormulaService;
use Illuminate\Http\Request;

class CsvFormulaController extends Controller
{
    protected $formulaService;
    
    public function __construct(FormulaService $formulaService)
    {
        $this->formulaService = $formulaService;
    }
    
    /**
     * Get all available formula definitions.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function definitions()
    {
        $definitions = $this->formulaService->getFormulaDefinitions();
        
        return response()->json([
            'success' => true,
            'definitions' => $definitions,
        ]);
    }
    
    /**
     * Apply a formula to a sample value.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function preview(Request $request)
    {
        $request->validate([
            'formula' => 'required|json',
            'value' => 'required|string',
            'row' => 'nullable|json',
        ]);
        
        try {
            $formula = json_decode($request->input('formula'), true);
            $value = $request->input('value');
            $row = json_decode($request->input('row') ?? '{}', true);
            
            $result = $this->formulaService->applyFormula($value, $formula, $row);
            
            return response()->json([
                'success' => true,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Formula application failed: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Apply multiple formulas to a sample value.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function previewMultiple(Request $request)
    {
        $request->validate([
            'formulas' => 'required|json',
            'value' => 'required|string',
            'row' => 'nullable|json',
        ]);
        
        try {
            $formulas = json_decode($request->input('formulas'), true);
            $value = $request->input('value');
            $row = json_decode($request->input('row') ?? '{}', true);
            
            $result = $this->formulaService->applyFormulas($value, $formulas, $row);
            
            return response()->json([
                'success' => true,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Formula application failed: ' . $e->getMessage(),
            ], 500);
        }
    }
}
