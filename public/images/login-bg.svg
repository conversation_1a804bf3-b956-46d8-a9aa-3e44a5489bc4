<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="1200px" viewBox="0 0 800 1200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid slice">
    <title>Login Background Enhanced</title>
    <defs>
        <!-- Main background gradient -->
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="backgroundGradient">
            <stop stop-color="#403d56" offset="0%"></stop>
            <stop stop-color="#2d2a3d" offset="100%"></stop>
        </linearGradient>
        
        <!-- Accent gradients -->
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="accentGradient1">
            <stop stop-color="#198856" offset="0%"></stop>
            <stop stop-color="#0d6e3f" offset="100%"></stop>
        </linearGradient>
        
        <!-- Glow effects -->
        <radialGradient id="glow1" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
            <stop offset="0%" stop-color="#198856" stop-opacity="0.15" />
            <stop offset="100%" stop-color="#198856" stop-opacity="0" />
        </radialGradient>
        
        <radialGradient id="glow2" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
            <stop offset="0%" stop-color="#ffffff" stop-opacity="0.05" />
            <stop offset="100%" stop-color="#ffffff" stop-opacity="0" />
        </radialGradient>
        
        <!-- Patterns -->
        <pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
            <circle cx="10" cy="10" r="1" fill="#ffffff" fill-opacity="0.2" />
        </pattern>
        
        <!-- Filters -->
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="5" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
    </defs>
    
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Main background -->
        <rect fill="url(#backgroundGradient)" x="0" y="0" width="800" height="1200"></rect>
        
        <!-- Decorative glows -->
        <circle cx="400" cy="300" r="300" fill="url(#glow1)" opacity="0.8"></circle>
        <circle cx="200" cy="900" r="200" fill="url(#glow2)" opacity="0.5"></circle>
        
        <!-- Dot pattern overlay -->
        <rect fill="url(#dots)" x="0" y="0" width="800" height="1200" opacity="0.3"></rect>
        
        <!-- Decorative shapes -->
        <g opacity="0.6">
            <!-- Top left decorative shape -->
            <path d="M-50,100 C50,50 100,150 200,100 C300,50 350,150 450,100" 
                  stroke="#198856" stroke-width="3" stroke-opacity="0.5" fill="none"></path>
            <path d="M-50,110 C50,60 100,160 200,110 C300,60 350,160 450,110" 
                  stroke="#ffffff" stroke-width="1" stroke-opacity="0.3" fill="none"></path>
                  
            <!-- Bottom right decorative shape -->
            <path d="M350,1100 C450,1050 500,1150 600,1100 C700,1050 750,1150 850,1100" 
                  stroke="#198856" stroke-width="3" stroke-opacity="0.5" fill="none"></path>
            <path d="M350,1110 C450,1060 500,1160 600,1110 C700,1060 750,1160 850,1110" 
                  stroke="#ffffff" stroke-width="1" stroke-opacity="0.3" fill="none"></path>
        </g>
        
        <!-- Animated-looking data flow -->
        <g transform="translate(100, 200)" opacity="0.7">
            <!-- CSV-like data structure -->
            <rect x="0" y="0" width="600" height="200" rx="8" fill="#ffffff" fill-opacity="0.05" stroke="#198856" stroke-opacity="0.2" stroke-width="1"></rect>
            
            <!-- Headers -->
            <rect x="20" y="20" width="560" height="30" rx="4" fill="#198856" fill-opacity="0.2"></rect>
            
            <!-- Column dividers -->
            <line x1="170" y1="20" x2="170" y2="180" stroke="#ffffff" stroke-width="1" stroke-opacity="0.2"></line>
            <line x1="320" y1="20" x2="320" y2="180" stroke="#ffffff" stroke-width="1" stroke-opacity="0.2"></line>
            <line x1="470" y1="20" x2="470" y2="180" stroke="#ffffff" stroke-width="1" stroke-opacity="0.2"></line>
            
            <!-- Data rows -->
            <rect x="20" y="60" width="560" height="20" rx="2" fill="#ffffff" fill-opacity="0.1"></rect>
            <rect x="20" y="90" width="560" height="20" rx="2" fill="#ffffff" fill-opacity="0.1"></rect>
            <rect x="20" y="120" width="560" height="20" rx="2" fill="#ffffff" fill-opacity="0.1"></rect>
            <rect x="20" y="150" width="560" height="20" rx="2" fill="#ffffff" fill-opacity="0.1"></rect>
            
            <!-- Highlight cells -->
            <rect x="170" y="60" width="150" height="20" rx="2" fill="#198856" fill-opacity="0.3"></rect>
            <rect x="320" y="90" width="150" height="20" rx="2" fill="#198856" fill-opacity="0.3"></rect>
            <rect x="20" y="120" width="150" height="20" rx="2" fill="#198856" fill-opacity="0.3"></rect>
            <rect x="470" y="150" width="110" height="20" rx="2" fill="#198856" fill-opacity="0.3"></rect>
        </g>
        
        <!-- Transformation arrow -->
        <g transform="translate(400, 450)" opacity="0.8">
            <path d="M0,-30 L0,30 M-15,15 L0,30 L15,15" stroke="#198856" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" filter="url(#glow)"></path>
        </g>
        
        <!-- Transformed data visualization -->
        <g transform="translate(200, 500)" opacity="0.7">
            <!-- Bar chart -->
            <rect x="0" y="0" width="400" height="150" rx="8" fill="#ffffff" fill-opacity="0.05" stroke="#198856" stroke-opacity="0.2" stroke-width="1"></rect>
            
            <!-- Bars -->
            <rect x="50" y="30" width="30" height="80" rx="2" fill="#198856" fill-opacity="0.6"></rect>
            <rect x="100" y="50" width="30" height="60" rx="2" fill="#198856" fill-opacity="0.6"></rect>
            <rect x="150" y="20" width="30" height="90" rx="2" fill="#198856" fill-opacity="0.6"></rect>
            <rect x="200" y="40" width="30" height="70" rx="2" fill="#198856" fill-opacity="0.6"></rect>
            <rect x="250" y="10" width="30" height="100" rx="2" fill="#198856" fill-opacity="0.6"></rect>
            <rect x="300" y="30" width="30" height="80" rx="2" fill="#198856" fill-opacity="0.6"></rect>
            
            <!-- Baseline -->
            <line x1="30" y1="110" x2="370" y2="110" stroke="#ffffff" stroke-width="1" stroke-opacity="0.3"></line>
        </g>
        
        <!-- Connected nodes visualization -->
        <g transform="translate(150, 700)" opacity="0.7">
            <!-- Network diagram -->
            <rect x="0" y="0" width="500" height="200" rx="8" fill="#ffffff" fill-opacity="0.05" stroke="#198856" stroke-opacity="0.2" stroke-width="1"></rect>
            
            <!-- Nodes -->
            <circle cx="100" cy="70" r="20" fill="#198856" fill-opacity="0.4" stroke="#198856" stroke-opacity="0.6" stroke-width="1"></circle>
            <circle cx="250" cy="50" r="20" fill="#198856" fill-opacity="0.4" stroke="#198856" stroke-opacity="0.6" stroke-width="1"></circle>
            <circle cx="400" cy="80" r="20" fill="#198856" fill-opacity="0.4" stroke="#198856" stroke-opacity="0.6" stroke-width="1"></circle>
            <circle cx="150" cy="150" r="20" fill="#198856" fill-opacity="0.4" stroke="#198856" stroke-opacity="0.6" stroke-width="1"></circle>
            <circle cx="300" cy="130" r="20" fill="#198856" fill-opacity="0.4" stroke="#198856" stroke-opacity="0.6" stroke-width="1"></circle>
            
            <!-- Connections -->
            <line x1="100" y1="70" x2="250" y2="50" stroke="#198856" stroke-width="2" stroke-opacity="0.4"></line>
            <line x1="250" y1="50" x2="400" y2="80" stroke="#198856" stroke-width="2" stroke-opacity="0.4"></line>
            <line x1="100" y1="70" x2="150" y2="150" stroke="#198856" stroke-width="2" stroke-opacity="0.4"></line>
            <line x1="150" y1="150" x2="300" y2="130" stroke="#198856" stroke-width="2" stroke-opacity="0.4"></line>
            <line x1="250" y1="50" x2="300" y2="130" stroke="#198856" stroke-width="2" stroke-opacity="0.4"></line>
            <line x1="400" y1="80" x2="300" y2="130" stroke="#198856" stroke-width="2" stroke-opacity="0.4"></line>
        </g>
        
        <!-- Floating particles -->
        <g opacity="0.7">
            <circle cx="100" cy="100" r="3" fill="#ffffff"></circle>
            <circle cx="200" cy="150" r="2" fill="#ffffff"></circle>
            <circle cx="300" cy="100" r="4" fill="#ffffff"></circle>
            <circle cx="400" cy="200" r="2" fill="#ffffff"></circle>
            <circle cx="500" cy="150" r="3" fill="#ffffff"></circle>
            <circle cx="600" cy="100" r="2" fill="#ffffff"></circle>
            <circle cx="700" cy="200" r="3" fill="#ffffff"></circle>
            <circle cx="150" cy="250" r="2" fill="#ffffff"></circle>
            <circle cx="250" cy="350" r="3" fill="#ffffff"></circle>
            <circle cx="350" cy="300" r="2" fill="#ffffff"></circle>
            <circle cx="450" cy="400" r="4" fill="#ffffff"></circle>
            <circle cx="550" cy="350" r="2" fill="#ffffff"></circle>
            <circle cx="650" cy="450" r="3" fill="#ffffff"></circle>
            
            <circle cx="100" cy="950" r="3" fill="#ffffff"></circle>
            <circle cx="200" cy="900" r="2" fill="#ffffff"></circle>
            <circle cx="300" cy="1000" r="4" fill="#ffffff"></circle>
            <circle cx="400" cy="950" r="2" fill="#ffffff"></circle>
            <circle cx="500" cy="1050" r="3" fill="#ffffff"></circle>
            <circle cx="600" cy="1000" r="2" fill="#ffffff"></circle>
            <circle cx="700" cy="1100" r="3" fill="#ffffff"></circle>
        </g>
        
        <!-- Accent circles -->
        <g opacity="0.5">
            <circle cx="700" cy="200" r="6" fill="#198856"></circle>
            <circle cx="100" cy="400" r="8" fill="#198856"></circle>
            <circle cx="650" cy="600" r="5" fill="#198856"></circle>
            <circle cx="200" cy="800" r="7" fill="#198856"></circle>
            <circle cx="550" cy="1000" r="6" fill="#198856"></circle>
        </g>
    </g>
</svg>
