<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>About Story Illustration</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#e6f7f1" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="url(#linearGradient-1)" x="0" y="0" width="800" height="600" rx="12"></rect>
        
        <!-- Document Icon -->
        <g transform="translate(250, 150)">
            <rect fill="#FFFFFF" x="0" y="0" width="300" height="350" rx="8"></rect>
            <rect fill="#e6f7f1" x="30" y="40" width="240" height="20" rx="4"></rect>
            <rect fill="#e6f7f1" x="30" y="80" width="180" height="20" rx="4"></rect>
            <rect fill="#e6f7f1" x="30" y="120" width="240" height="20" rx="4"></rect>
            <rect fill="#e6f7f1" x="30" y="160" width="120" height="20" rx="4"></rect>
            
            <!-- CSV Data Representation -->
            <rect stroke="#e6f7f1" stroke-width="2" fill="#FFFFFF" x="30" y="200" width="240" height="120" rx="4"></rect>
            <line x1="90" y1="200" x2="90" y2="320" stroke="#e6f7f1" stroke-width="2"></line>
            <line x1="150" y1="200" x2="150" y2="320" stroke="#e6f7f1" stroke-width="2"></line>
            <line x1="210" y1="200" x2="210" y2="320" stroke="#e6f7f1" stroke-width="2"></line>
            <line x1="30" y1="230" x2="270" y2="230" stroke="#e6f7f1" stroke-width="2"></line>
            <line x1="30" y1="260" x2="270" y2="260" stroke="#e6f7f1" stroke-width="2"></line>
            <line x1="30" y1="290" x2="270" y2="290" stroke="#e6f7f1" stroke-width="2"></line>
        </g>
        
        <!-- People Icons -->
        <g transform="translate(150, 200)">
            <circle fill="#e6f7f1" cx="30" cy="30" r="30"></circle>
            <circle fill="#FFFFFF" cx="30" cy="15" r="12"></circle>
            <path d="M10,60 C10,40 50,40 50,60" stroke="#FFFFFF" stroke-width="4" fill="none"></path>
        </g>
        
        <g transform="translate(600, 250)">
            <circle fill="#e6f7f1" cx="30" cy="30" r="30"></circle>
            <circle fill="#FFFFFF" cx="30" cy="15" r="12"></circle>
            <path d="M10,60 C10,40 50,40 50,60" stroke="#FFFFFF" stroke-width="4" fill="none"></path>
        </g>
        
        <!-- Connection Lines -->
        <path d="M200,230 C250,180 350,150 250,150" stroke="#e6f7f1" stroke-width="3" fill="none" stroke-dasharray="5,5"></path>
        <path d="M600,280 C550,300 450,350 550,350" stroke="#e6f7f1" stroke-width="3" fill="none" stroke-dasharray="5,5"></path>
    </g>
</svg>
