import AppNavbar from '@/components/public/app-navbar';
import AppFooter from '@/components/public/app-footer';
import type { PropsWithChildren } from 'react';

interface AppPublicLayoutProps extends PropsWithChildren {
    user: object; // or the appropriate type for 'auth'
}

export default function AppPublicLayout({ children, user }: AppPublicLayoutProps)  {
    return (
        <>
            <div className="flex min-h-screen flex-col bg-[#FDFDFC] text-[#1b1b18] dark:bg-background dark:text-[#EDEDEC] transition-colors duration-200">
            <AppNavbar user={user} />
            <div className="w-full flex-grow">
                {children}
            </div>
            <AppFooter />
            </div>
        </>
    );
};
