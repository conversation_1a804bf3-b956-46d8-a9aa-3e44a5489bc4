import AppLogo from '@/components/app-logo';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    title?: string;
    description?: string;
}

export default function AuthEnhancedLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="relative grid h-dvh flex-col items-center justify-center lg:max-w-none lg:grid-cols-2 lg:px-0">
            {/* Left side - Enhanced visual section */}
            <div className="relative hidden h-full w-full overflow-hidden lg:flex">
                {/* Background gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#403d56]/90 to-[#403d56]/70 z-10"></div>

                {/* Background image */}
                <img
                    src="/images/login-bg.svg"
                    alt="Login background"
                    className="absolute inset-0 h-full w-full object-cover"
                />

                {/* Content overlay */}
                <div className="relative z-20 flex flex-col p-8 text-white w-full h-full">
                    {/* Logo and brand */}
                    <div>
                        <Link href={route('home')} className="flex items-center justify-center text-lg font-medium">
                            <AppLogo />
                        </Link>
                    </div>

                    {/* Spacer to push content to center */}
                    <div className="flex-grow"></div>

                    {/* Main content - vertically centered */}
                    <div className="max-w-md mx-auto text-center relative">
                        {/* Floating 3D CSV icon */}
                        <div className="absolute -top-20 -right-20 opacity-20 animate-bounce" style={{animationDuration: '6s'}}>
                            <div className="relative w-24 h-32 bg-[#198856]/20 rounded-md border border-[#198856]/30 transform rotate-12 backdrop-blur-sm">
                                <div className="absolute inset-2 border border-dashed border-white/30 rounded-sm"></div>
                                <div className="absolute top-4 left-4 w-16 h-2 bg-white/20 rounded-sm"></div>
                                <div className="absolute top-8 left-4 w-12 h-2 bg-white/20 rounded-sm"></div>
                                <div className="absolute top-12 left-4 w-14 h-2 bg-white/20 rounded-sm"></div>
                                <div className="absolute top-16 left-4 w-10 h-2 bg-white/20 rounded-sm"></div>
                                <div className="absolute top-20 left-4 w-16 h-2 bg-white/20 rounded-sm"></div>
                                <div className="absolute top-24 left-4 w-12 h-2 bg-white/20 rounded-sm"></div>
                            </div>
                        </div>
                        <div className="relative inline-block">
                            <div className="absolute -inset-1 bg-gradient-to-r from-[#198856] via-[#198856]/50 to-[#403d56] rounded-lg blur-lg opacity-75 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse"></div>
                            <div className="relative px-7 py-4 bg-black bg-opacity-80 rounded-lg leading-none flex items-center">
                                <span className="flex items-center space-x-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#198856] animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span className="text-gray-100 text-lg">CSV</span>
                                </span>
                                <span className="text-[#198856] px-2">→</span>
                                <span className="flex items-center space-x-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#198856] animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                    <span className="text-gray-100 text-lg">Transform</span>
                                </span>
                            </div>
                        </div>

                        <h1 className="text-3xl font-bold mt-6 mb-3 font-serif bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300 relative z-10">Powerful CSV Transformation</h1>

                        <div className="relative mb-8">
                            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-[#198856]/10 rounded-full blur-3xl -z-10 animate-pulse"></div>
                            <div className="flex justify-center">
                                <div className="px-4 py-1 rounded-full bg-[#198856]/20 border border-[#198856]/30 flex items-center space-x-2 backdrop-blur-sm shadow-lg">
                                    <span className="animate-pulse text-[#198856]">●</span>
                                    <p className="text-lg">Streamline your data workflow with intelligent tools</p>
                                </div>
                            </div>

                            {/* Animated data flow */}
                            <div className="absolute -top-12 -left-20 opacity-20">
                                <div className="w-2 h-2 bg-white rounded-full animate-ping" style={{ animationDelay: '0.5s', animationDuration: '3s' }}></div>
                            </div>
                            <div className="absolute -top-8 left-10 opacity-20">
                                <div className="w-2 h-2 bg-white rounded-full animate-ping" style={{ animationDelay: '1.2s', animationDuration: '2.7s' }}></div>
                            </div>
                            <div className="absolute -top-4 right-10 opacity-20">
                                <div className="w-2 h-2 bg-white rounded-full animate-ping" style={{ animationDelay: '0.8s', animationDuration: '3.2s' }}></div>
                            </div>
                            <div className="absolute -bottom-12 -right-20 opacity-20">
                                <div className="w-2 h-2 bg-white rounded-full animate-ping" style={{ animationDelay: '1.5s', animationDuration: '2.5s' }}></div>
                            </div>
                        </div>

                        {/* Feature list */}
                        <div className="space-y-3 text-left relative">
                            {/* Connecting lines between feature cards */}
                            <div className="absolute left-4 top-11 w-0.5 h-[calc(100%-24px)] bg-gradient-to-b from-[#198856]/50 to-transparent z-0"></div>

                            <div className="flex items-start bg-[#198856]/10 p-3 rounded-lg border border-[#198856]/20 backdrop-blur-sm relative z-10 transform transition-transform duration-300 hover:scale-[1.02] hover:shadow-lg">
                                <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-[#198856] rounded-full"></div>
                                <div className="flex-shrink-0 h-8 w-8 rounded-lg bg-[#198856] flex items-center justify-center mr-3 shadow-md shadow-[#198856]/20">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-bold text-base mb-0.5">One-Click Import & Export</h3>
                                    <p className="text-white/80 text-sm">Drag-and-drop interface with smart field mapping</p>
                                </div>
                            </div>
                            <div className="flex items-start bg-[#198856]/10 p-3 rounded-lg border border-[#198856]/20 backdrop-blur-sm relative z-10 transform transition-transform duration-300 hover:scale-[1.02] hover:shadow-lg">
                                <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-[#198856] rounded-full"></div>
                                <div className="flex-shrink-0 h-8 w-8 rounded-lg bg-[#198856] flex items-center justify-center mr-3 shadow-md shadow-[#198856]/20">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-bold text-base mb-0.5">Custom Templates</h3>
                                    <p className="text-white/80 text-sm">Create, save, and share your transformation recipes</p>
                                </div>
                            </div>
                            <div className="flex items-start bg-[#198856]/10 p-3 rounded-lg border border-[#198856]/20 backdrop-blur-sm relative z-10 transform transition-transform duration-300 hover:scale-[1.02] hover:shadow-lg">
                                <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-[#198856] rounded-full"></div>
                                <div className="flex-shrink-0 h-8 w-8 rounded-lg bg-[#198856] flex items-center justify-center mr-3 shadow-md shadow-[#198856]/20">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.7519 11.1679L11.5547 9.03647M11.5547 9.03647L12.6181 5.5M11.5547 9.03647L8.5 11.5M7.5 3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V7.5M7.5 20.5H5.5C4.39543 20.5 3.5 19.6046 3.5 18.5V16.5M16.5 3.5H18.5C19.6046 3.5 20.5 4.39543 20.5 5.5V7.5M16.5 20.5H18.5C19.6046 20.5 20.5 19.6046 20.5 18.5V16.5" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-bold text-base mb-0.5">Smart Automation</h3>
                                    <p className="text-white/80 text-sm">Schedule and batch process multiple files at once</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Spacer to push testimonial to bottom */}
                    <div className="flex-grow"></div>

                    {/* Testimonial */}
                    <div className="mt-auto">
                        <blockquote className="p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 relative">
                            <svg className="absolute -top-3 -left-1 h-6 w-6 text-[#198856]" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
                                <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                            </svg>
                            <p className="text-base italic relative z-10">"Bulkify Connect has saved our team countless hours of manual data processing."</p>
                            <div className="mt-2 flex items-center">
                                <img className="h-8 w-8 rounded-full border border-[#198856]" src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah Johnson" />
                                <div className="ml-2">
                                    <p className="font-semibold text-sm">Sarah Johnson</p>
                                    <p className="text-xs text-white/80">Data Operations Manager</p>
                                </div>
                            </div>
                        </blockquote>
                    </div>
                </div>
            </div>

            {/* Right side - Login form */}
            <div className="w-full bg-white dark:bg-gray-900 px-6 py-8 lg:p-12">
                <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
                    {/* Mobile logo */}
                    <Link href={route('home')} className="relative z-20 flex items-center justify-center lg:hidden">
                        <AppLogo />
                    </Link>

                    {/* Form header */}
                    <div className="flex flex-col items-start gap-2 text-left">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
                        <p className="text-muted-foreground text-sm text-balance">{description}</p>
                    </div>

                    {/* Form content */}
                    {children}
                </div>
            </div>
        </div>
    );
}
