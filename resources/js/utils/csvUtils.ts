import { CSVData, CSVColumn, CSVRow, Formula, FormulaType } from '../types/csv';
import axios from 'axios';

/**
 * Parse a CSV string into a 2D array of strings
 */
export const parseCSV = (text: string): string[][] => {
  const rows: string[][] = [];
  const lines = text.split('\n');
  
  for (const line of lines) {
    if (!line.trim()) continue;
    
    const row: string[] = [];
    let inQuotes = false;
    let currentValue = '';
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      const nextChar = line[i + 1];
      
      if (char === '"' && inQuotes && nextChar === '"') {
        // <PERSON>le escaped quotes
        currentValue += '"';
        i++;
      } else if (char === '"') {
        // Toggle quotes mode
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        // End of field
        row.push(currentValue);
        currentValue = '';
      } else {
        // Normal character
        currentValue += char;
      }
    }
    
    // Don't forget the last field
    row.push(currentValue);
    rows.push(row);
  }
  
  return rows;
};

/**
 * Create a structured CSV data object from parsed rows
 */
export const createCSVData = (parsedData: string[][], fileName: string): CSVData => {
  if (parsedData.length === 0) {
    return { columns: [], rows: [], fileName };
  }
  
  const headerRow = parsedData[0];
  const columns: CSVColumn[] = headerRow.map((name, index) => ({
    id: `col-${index}`,
    originalName: name,
    mappedName: name,
    formulas: []
  }));
  
  const rows: CSVRow[] = parsedData.slice(1).map((row, rowIndex) => {
    const data: Record<string, string> = {};
    row.forEach((value, colIndex) => {
      if (colIndex < columns.length) {
        data[columns[colIndex].id] = value;
      }
    });
    
    return {
      id: `row-${rowIndex}`,
      data,
      included: true
    };
  });
  
  return { columns, rows, fileName };
};

/**
 * Apply a formula to a value using the backend API
 */
export const applyFormula = async (value: string, formula: Formula, row: Record<string, string>): Promise<string> => {
  try {
    const response = await axios.post(route('csv.formulas.preview'), {
      formula: JSON.stringify(formula),
      value,
      row: JSON.stringify(row)
    });
    
    if (response.data.success) {
      return response.data.result;
    } else {
      console.error('Formula application failed:', response.data.message);
      return `Error: ${response.data.message}`;
    }
  } catch (error) {
    console.error('Formula application error:', error);
    return `Error: ${error}`;
  }
};

/**
 * Apply multiple formulas to a value using the backend API
 */
export const applyFormulas = async (value: string, formulas: Formula[], row: Record<string, string>): Promise<string> => {
  if (!formulas || formulas.length === 0) return value;
  
  try {
    const response = await axios.post(route('csv.formulas.preview-multiple'), {
      formulas: JSON.stringify(formulas),
      value,
      row: JSON.stringify(row)
    });
    
    if (response.data.success) {
      return response.data.result;
    } else {
      console.error('Formula application failed:', response.data.message);
      return `Error: ${response.data.message}`;
    }
  } catch (error) {
    console.error('Formula application error:', error);
    return `Error: ${error}`;
  }
};

/**
 * Generate a CSV string from the provided data
 */
export const generateCSVString = (data: CSVData): string => {
  // Create the header row using the mapped column names
  const headerRow = data.columns.map(col => {
    // Escape any quotes in the header
    const escapedName = col.mappedName.replace(/"/g, '""');
    return `"${escapedName}"`;
  }).join(',');

  // Create the data rows
  const dataRows = data.rows
    .filter(row => row.included)
    .map(row => {
      return data.columns.map(col => {
        // Get the value for this cell
        let value = row.data[col.id] || '';
        
        // Apply formulas if they exist for this column
        // Note: This is a placeholder. In the real implementation,
        // we would use the backend to apply formulas
        
        // Escape any quotes in the value
        const escapedValue = value.replace(/"/g, '""');
        return `"${escapedValue}"`;
      }).join(',');
    })
    .join('\n');

  // Combine header and data rows
  return `${headerRow}\n${dataRows}`;
};

/**
 * Export CSV data to a file using the backend API
 */
export const exportCSV = async (data: CSVData): Promise<string> => {
  try {
    const response = await axios.post(route('csv.export'), {
      data: JSON.stringify(data),
      file_name: data.fileName
    });
    
    if (response.data.success) {
      return response.data.download_url;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('Export error:', error);
    throw error;
  }
};
