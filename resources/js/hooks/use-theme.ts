import { useState, useEffect } from 'react';

type Theme = 'light' | 'dark';

// Helper function to set a cookie (same as in use-appearance.tsx)
const setCookie = (name: string, value: string, days = 365) => {
    if (typeof document === 'undefined') {
        return;
    }

    const maxAge = days * 24 * 60 * 60;
    document.cookie = `${name}=${value};path=/;max-age=${maxAge};SameSite=Lax`;
};

export function useTheme() {
    const [theme, setTheme] = useState<Theme>(() => {
        // Check if theme is stored in localStorage using the 'appearance' key
        const storedAppearance = localStorage.getItem('appearance');
        if (storedAppearance === 'light' || storedAppearance === 'dark') {
            return storedAppearance;
        }

        // Check user preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }

        return 'light';
    });

    useEffect(() => {
        // Update localStorage using the 'appearance' key for consistency
        localStorage.setItem('appearance', theme);

        // Also set the cookie for SSR consistency
        setCookie('appearance', theme);

        // Update document class
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }, [theme]);

    return { theme, setTheme };
}
