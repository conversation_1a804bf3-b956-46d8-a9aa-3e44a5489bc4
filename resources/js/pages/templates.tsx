import React, { useState } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';
import AppDashboardLayout from '@/layouts/app-dashboard-layout';
import {
    FileText,
    Search,
    Plus,
    MoreHorizontal,
    Edit,
    Trash2,
    Copy,
    Download,
    Calendar,
    Clock,
    Tag,
    Filter
} from 'lucide-react';

// Template Card Component
interface TemplateCardProps {
    id: string;
    name: string;
    description: string;
    tags: string[];
    lastUsed: string;
    usageCount: number;
    createdAt: string;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ id, name, description, tags, lastUsed, usageCount, createdAt }) => {
    const [showMenu, setShowMenu] = useState(false);

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                    <div className="p-3 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                        <FileText className="h-6 w-6" />
                    </div>
                    <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">{name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{description}</p>
                    </div>
                </div>
                <div className="relative">
                    <button
                        className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        onClick={() => setShowMenu(!showMenu)}
                    >
                        <MoreHorizontal className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    </button>
                    {showMenu && (
                        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700">
                            <div className="py-1">
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit Template
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <Copy className="h-4 w-4 mr-2" />
                                    Duplicate
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <Download className="h-4 w-4 mr-2" />
                                    Export
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <div className="mt-4 flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                    <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#198856]/10 text-[#198856]"
                    >
                        {tag}
                    </span>
                ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 grid grid-cols-3 gap-2 text-sm">
                <div className="flex flex-col">
                    <span className="text-gray-500 dark:text-gray-400 flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        Last Used
                    </span>
                    <span className="font-medium">{lastUsed}</span>
                </div>
                <div className="flex flex-col">
                    <span className="text-gray-500 dark:text-gray-400 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        Created
                    </span>
                    <span className="font-medium">{createdAt}</span>
                </div>
                <div className="flex flex-col">
                    <span className="text-gray-500 dark:text-gray-400 flex items-center">
                        <FileText className="h-3 w-3 mr-1" />
                        Usage
                    </span>
                    <span className="font-medium">{usageCount} times</span>
                </div>
            </div>
        </div>
    );
};

export default function Templates() {
    const { auth } = usePage<SharedData>().props;
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedTags, setSelectedTags] = useState<string[]>([]);

    const allTags = ['Sales', 'Inventory', 'Customer', 'Finance', 'Marketing', 'Product', 'Shipping'];

    const templates = [
        {
            id: '1',
            name: 'Sales Data Formatter',
            description: 'Formats and validates sales data CSV files with automatic currency conversion and date standardization.',
            tags: ['Sales', 'Finance'],
            lastUsed: '3 hours ago',
            usageCount: 42,
            createdAt: 'Jan 15, 2023'
        },
        {
            id: '2',
            name: 'Customer Data Cleaner',
            description: 'Cleans and normalizes customer information including address formatting and duplicate detection.',
            tags: ['Customer', 'Marketing'],
            lastUsed: 'Yesterday',
            usageCount: 28,
            createdAt: 'Mar 22, 2023'
        },
        {
            id: '3',
            name: 'Inventory Updater',
            description: 'Prepares inventory data for system import with SKU validation and stock level calculations.',
            tags: ['Inventory', 'Product'],
            lastUsed: '3 days ago',
            usageCount: 15,
            createdAt: 'Apr 10, 2023'
        },
        {
            id: '4',
            name: 'Marketing Campaign Analyzer',
            description: 'Processes campaign data and calculates key performance metrics for marketing analysis.',
            tags: ['Marketing', 'Sales'],
            lastUsed: '1 week ago',
            usageCount: 8,
            createdAt: 'May 5, 2023'
        },
        {
            id: '5',
            name: 'Shipping Log Processor',
            description: 'Standardizes shipping logs from multiple carriers and calculates delivery performance metrics.',
            tags: ['Shipping', 'Inventory'],
            lastUsed: '2 weeks ago',
            usageCount: 12,
            createdAt: 'Jun 18, 2023'
        },
        {
            id: '6',
            name: 'Financial Report Generator',
            description: 'Transforms raw transaction data into structured financial reports with automatic categorization.',
            tags: ['Finance'],
            lastUsed: '1 month ago',
            usageCount: 6,
            createdAt: 'Jul 30, 2023'
        }
    ];

    const toggleTag = (tag: string) => {
        if (selectedTags.includes(tag)) {
            setSelectedTags(selectedTags.filter(t => t !== tag));
        } else {
            setSelectedTags([...selectedTags, tag]);
        }
    };

    const filteredTemplates = templates.filter(template => {
        const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             template.description.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesTags = selectedTags.length === 0 ||
                           selectedTags.some(tag => template.tags.includes(tag));

        return matchesSearch && matchesTags;
    });

    return (
        <AppDashboardLayout user={auth.user}>
            <Head title="Templates" />

            <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 className="text-2xl font-bold">Templates</h1>
                    <p className="text-gray-500 dark:text-gray-400 mt-1">Create and manage your CSV transformation templates</p>
                </div>
                <button className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#198856] hover:bg-[#198856]/90 dark:bg-[#198856] dark:hover:bg-[#198856]/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#198856] dark:focus:ring-offset-gray-900 transition-colors duration-200">
                    <Plus className="h-4 w-4 mr-2" />
                    New Template
                </button>
            </div>

            {/* Search and Filter */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4">
                <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search templates..."
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-[#198856] focus:border-[#198856] sm:text-sm"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
                <div className="relative">
                    <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#198856] dark:focus:ring-offset-gray-900 transition-colors duration-200">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                        {selectedTags.length > 0 && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#198856] text-white">
                                {selectedTags.length}
                            </span>
                        )}
                    </button>
                </div>
            </div>

            {/* Tags */}
            <div className="mb-6 flex flex-wrap gap-2">
                {allTags.map((tag) => (
                    <button
                        key={tag}
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${
                            selectedTags.includes(tag)
                                ? 'bg-[#198856] text-white dark:bg-[#198856] dark:text-white'
                                : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                        onClick={() => toggleTag(tag)}
                    >
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                    </button>
                ))}
            </div>

            {/* Templates Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTemplates.map((template) => (
                    <TemplateCard
                        key={template.id}
                        id={template.id}
                        name={template.name}
                        description={template.description}
                        tags={template.tags}
                        lastUsed={template.lastUsed}
                        usageCount={template.usageCount}
                        createdAt={template.createdAt}
                    />
                ))}
            </div>

            {/* Empty State */}
            {filteredTemplates.length === 0 && (
                <div className="text-center py-12">
                    <FileText className="h-12 w-12 mx-auto text-gray-400" />
                    <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No templates found</h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {searchQuery || selectedTags.length > 0
                            ? 'Try adjusting your search or filters'
                            : 'Get started by creating your first template'}
                    </p>
                    {!(searchQuery || selectedTags.length > 0) && (
                        <div className="mt-6">
                            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#198856] hover:bg-[#198856]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#198856]">
                                <Plus className="h-4 w-4 mr-2" />
                                New Template
                            </button>
                        </div>
                    )}
                </div>
            )}
        </AppDashboardLayout>
    );
}
