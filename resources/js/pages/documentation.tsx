import React from 'react';
import { Head, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';
import AppDashboardLayout from '@/layouts/app-dashboard-layout';
import { BookOpen, Search, FileText, ArrowRight } from 'lucide-react';

// Documentation Category Component
interface DocCategoryProps {
    title: string;
    description: string;
    icon: React.ReactNode;
    articles: { title: string; path: string }[];
}

const DocCategory: React.FC<DocCategoryProps> = ({ title, description, icon, articles }) => {
    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-start space-x-4">
                <div className="p-3 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                    {icon}
                </div>
                <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{description}</p>
                </div>
            </div>
            <div className="mt-6 space-y-3">
                {articles.map((article, index) => (
                    <a 
                        key={index} 
                        href={article.path}
                        className="flex items-center justify-between p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                    >
                        <div className="flex items-center">
                            <FileText className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-sm font-medium text-gray-900 dark:text-white">{article.title}</span>
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                    </a>
                ))}
            </div>
        </div>
    );
};

export default function Documentation() {
    const { auth } = usePage<SharedData>().props;

    const docCategories = [
        {
            title: 'Getting Started',
            description: 'Learn the basics of Bulkify Connect and how to set up your account',
            icon: <BookOpen className="h-6 w-6" />,
            articles: [
                { title: 'Introduction to Bulkify Connect', path: '#' },
                { title: 'Creating Your First Template', path: '#' },
                { title: 'Connecting Data Sources', path: '#' },
                { title: 'Running Your First Transformation', path: '#' }
            ]
        },
        {
            title: 'Templates',
            description: 'Learn how to create and manage transformation templates',
            icon: <FileText className="h-6 w-6" />,
            articles: [
                { title: 'Template Basics', path: '#' },
                { title: 'Advanced Template Features', path: '#' },
                { title: 'Template Variables', path: '#' },
                { title: 'Sharing Templates', path: '#' }
            ]
        },
        {
            title: 'Data Sources',
            description: 'Connect to various data sources and manage your connections',
            icon: <BookOpen className="h-6 w-6" />,
            articles: [
                { title: 'Supported Data Sources', path: '#' },
                { title: 'Database Connections', path: '#' },
                { title: 'API Integrations', path: '#' },
                { title: 'Cloud Storage', path: '#' }
            ]
        },
        {
            title: 'Transformations',
            description: 'Learn about the transformation process and options',
            icon: <BookOpen className="h-6 w-6" />,
            articles: [
                { title: 'Transformation Basics', path: '#' },
                { title: 'Scheduling Transformations', path: '#' },
                { title: 'Monitoring and Alerts', path: '#' },
                { title: 'Troubleshooting', path: '#' }
            ]
        },
        {
            title: 'API Reference',
            description: 'Detailed documentation for the Bulkify Connect API',
            icon: <BookOpen className="h-6 w-6" />,
            articles: [
                { title: 'Authentication', path: '#' },
                { title: 'Templates API', path: '#' },
                { title: 'Transformations API', path: '#' },
                { title: 'Webhooks', path: '#' }
            ]
        },
        {
            title: 'FAQs',
            description: 'Frequently asked questions and answers',
            icon: <BookOpen className="h-6 w-6" />,
            articles: [
                { title: 'Account Management', path: '#' },
                { title: 'Billing and Subscriptions', path: '#' },
                { title: 'Security and Privacy', path: '#' },
                { title: 'Troubleshooting', path: '#' }
            ]
        }
    ];

    return (
        <AppDashboardLayout user={auth.user}>
            <Head title="Documentation" />

            <div className="mb-6">
                <h1 className="text-2xl font-bold">Documentation</h1>
                <p className="text-gray-500 dark:text-gray-400 mt-1">Learn how to use Bulkify Connect effectively</p>
            </div>

            {/* Search */}
            <div className="mb-8">
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search documentation..."
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-[#198856] focus:border-[#198856] sm:text-sm"
                    />
                </div>
            </div>

            {/* Documentation Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {docCategories.map((category, index) => (
                    <DocCategory
                        key={index}
                        title={category.title}
                        description={category.description}
                        icon={category.icon}
                        articles={category.articles}
                    />
                ))}
            </div>
        </AppDashboardLayout>
    );
}
