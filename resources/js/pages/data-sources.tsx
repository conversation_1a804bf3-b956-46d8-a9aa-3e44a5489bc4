import React, { useState } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';
import AppDashboardLayout from '@/layouts/app-dashboard-layout';
import { 
    Database, 
    Plus, 
    FileText, 
    Cloud, 
    Server, 
    Table, 
    Globe, 
    CheckCircle, 
    XCircle,
    RefreshCw,
    MoreHorizontal,
    Edit,
    Trash2,
    ExternalLink
} from 'lucide-react';

// Data Source Card Component
interface DataSourceCardProps {
    id: string;
    name: string;
    type: 'database' | 'file' | 'api' | 'cloud';
    status: 'connected' | 'disconnected' | 'error';
    lastSync: string;
    description: string;
}

const DataSourceCard: React.FC<DataSourceCardProps> = ({ id, name, type, status, lastSync, description }) => {
    const [showMenu, setShowMenu] = useState(false);

    const getIcon = () => {
        switch (type) {
            case 'database':
                return <Database className="h-6 w-6" />;
            case 'file':
                return <FileText className="h-6 w-6" />;
            case 'api':
                return <Globe className="h-6 w-6" />;
            case 'cloud':
                return <Cloud className="h-6 w-6" />;
            default:
                return <Database className="h-6 w-6" />;
        }
    };

    const getIconBg = () => {
        switch (type) {
            case 'database':
                return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300';
            case 'file':
                return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300';
            case 'api':
                return 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300';
            case 'cloud':
                return 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300';
            default:
                return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-300';
        }
    };

    const getStatusColor = () => {
        switch (status) {
            case 'connected':
                return 'text-green-500';
            case 'disconnected':
                return 'text-gray-500';
            case 'error':
                return 'text-red-500';
            default:
                return 'text-gray-500';
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case 'connected':
                return <CheckCircle className="h-4 w-4 mr-1" />;
            case 'disconnected':
                return <XCircle className="h-4 w-4 mr-1" />;
            case 'error':
                return <XCircle className="h-4 w-4 mr-1" />;
            default:
                return <XCircle className="h-4 w-4 mr-1" />;
        }
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-md ${getIconBg()}`}>
                        {getIcon()}
                    </div>
                    <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">{name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{description}</p>
                    </div>
                </div>
                <div className="relative">
                    <button 
                        className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => setShowMenu(!showMenu)}
                    >
                        <MoreHorizontal className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    </button>
                    {showMenu && (
                        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700">
                            <div className="py-1">
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit Connection
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <RefreshCw className="h-4 w-4 mr-2" />
                                    Sync Now
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <ExternalLink className="h-4 w-4 mr-2" />
                                    View Details
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Disconnect
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <div className={`flex items-center ${getStatusColor()}`}>
                    {getStatusIcon()}
                    <span className="text-sm font-medium capitalize">{status}</span>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                    {status === 'connected' ? `Last synced ${lastSync}` : 'Not synced'}
                </div>
            </div>
        </div>
    );
};

// New Connection Card Component
interface NewConnectionCardProps {
    type: 'database' | 'file' | 'api' | 'cloud';
    title: string;
    description: string;
    onClick: () => void;
}

const NewConnectionCard: React.FC<NewConnectionCardProps> = ({ type, title, description, onClick }) => {
    const getIcon = () => {
        switch (type) {
            case 'database':
                return <Database className="h-8 w-8" />;
            case 'file':
                return <FileText className="h-8 w-8" />;
            case 'api':
                return <Globe className="h-8 w-8" />;
            case 'cloud':
                return <Cloud className="h-8 w-8" />;
            default:
                return <Database className="h-8 w-8" />;
        }
    };

    const getIconBg = () => {
        switch (type) {
            case 'database':
                return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300';
            case 'file':
                return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300';
            case 'api':
                return 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300';
            case 'cloud':
                return 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300';
            default:
                return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-300';
        }
    };

    return (
        <button 
            onClick={onClick}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-left hover:shadow-md transition-shadow duration-200"
        >
            <div className={`p-4 rounded-md ${getIconBg()} inline-block mb-4`}>
                {getIcon()}
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">{description}</p>
        </button>
    );
};

export default function DataSources() {
    const { auth } = usePage<SharedData>().props;
    const [showNewConnectionModal, setShowNewConnectionModal] = useState(false);
    const [activeTab, setActiveTab] = useState<'all' | 'connected' | 'disconnected'>('all');

    const dataSources = [
        {
            id: '1',
            name: 'MySQL Database',
            type: 'database' as const,
            status: 'connected' as const,
            lastSync: '2 hours ago',
            description: 'Primary product database with inventory and sales data'
        },
        {
            id: '2',
            name: 'Google Sheets',
            type: 'cloud' as const,
            status: 'connected' as const,
            lastSync: '1 day ago',
            description: 'Marketing campaign tracking spreadsheets'
        },
        {
            id: '3',
            name: 'Shopify API',
            type: 'api' as const,
            status: 'error' as const,
            lastSync: '3 days ago',
            description: 'E-commerce platform integration for order data'
        },
        {
            id: '4',
            name: 'Local CSV Directory',
            type: 'file' as const,
            status: 'connected' as const,
            lastSync: '12 hours ago',
            description: 'Exported reports from legacy systems'
        },
        {
            id: '5',
            name: 'PostgreSQL Analytics DB',
            type: 'database' as const,
            status: 'disconnected' as const,
            lastSync: 'Never',
            description: 'Data warehouse for business intelligence'
        },
        {
            id: '6',
            name: 'Salesforce CRM',
            type: 'api' as const,
            status: 'connected' as const,
            lastSync: '5 hours ago',
            description: 'Customer relationship management data'
        }
    ];

    const filteredDataSources = dataSources.filter(source => {
        if (activeTab === 'all') return true;
        if (activeTab === 'connected') return source.status === 'connected';
        if (activeTab === 'disconnected') return source.status === 'disconnected' || source.status === 'error';
        return true;
    });

    const connectionTypes = [
        {
            type: 'database' as const,
            title: 'Database Connection',
            description: 'Connect to MySQL, PostgreSQL, SQL Server, or other database systems'
        },
        {
            type: 'file' as const,
            title: 'File Storage',
            description: 'Connect to local or network file systems for CSV and other data files'
        },
        {
            type: 'api' as const,
            title: 'API Integration',
            description: 'Connect to REST APIs, webhooks, or other web services'
        },
        {
            type: 'cloud' as const,
            title: 'Cloud Storage',
            description: 'Connect to Google Drive, Dropbox, OneDrive, or other cloud storage'
        }
    ];

    return (
        <AppDashboardLayout user={auth.user}>
            <Head title="Data Sources" />

            <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 className="text-2xl font-bold">Data Sources</h1>
                    <p className="text-gray-500 dark:text-gray-400 mt-1">Manage connections to your data sources</p>
                </div>
                <button 
                    className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#198856] hover:bg-[#198856]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#198856]"
                    onClick={() => setShowNewConnectionModal(true)}
                >
                    <Plus className="h-4 w-4 mr-2" />
                    New Connection
                </button>
            </div>

            {/* Tabs */}
            <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
                <nav className="-mb-px flex space-x-8">
                    <button
                        className={`whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm ${
                            activeTab === 'all'
                                ? 'border-[#198856] text-[#198856]'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
                        }`}
                        onClick={() => setActiveTab('all')}
                    >
                        All Sources
                    </button>
                    <button
                        className={`whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm ${
                            activeTab === 'connected'
                                ? 'border-[#198856] text-[#198856]'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
                        }`}
                        onClick={() => setActiveTab('connected')}
                    >
                        Connected
                    </button>
                    <button
                        className={`whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm ${
                            activeTab === 'disconnected'
                                ? 'border-[#198856] text-[#198856]'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
                        }`}
                        onClick={() => setActiveTab('disconnected')}
                    >
                        Disconnected
                    </button>
                </nav>
            </div>

            {/* Data Sources Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredDataSources.map((source) => (
                    <DataSourceCard
                        key={source.id}
                        id={source.id}
                        name={source.name}
                        type={source.type}
                        status={source.status}
                        lastSync={source.lastSync}
                        description={source.description}
                    />
                ))}
            </div>

            {/* Empty State */}
            {filteredDataSources.length === 0 && (
                <div className="text-center py-12">
                    <Database className="h-12 w-12 mx-auto text-gray-400" />
                    <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No data sources found</h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {activeTab !== 'all'
                            ? `No ${activeTab} data sources found`
                            : 'Get started by connecting your first data source'}
                    </p>
                    {activeTab === 'all' && (
                        <div className="mt-6">
                            <button 
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#198856] hover:bg-[#198856]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#198856]"
                                onClick={() => setShowNewConnectionModal(true)}
                            >
                                <Plus className="h-4 w-4 mr-2" />
                                New Connection
                            </button>
                        </div>
                    )}
                </div>
            )}

            {/* New Connection Modal */}
            {showNewConnectionModal && (
                <div className="fixed inset-0 z-50 overflow-y-auto">
                    <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                            <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
                        </div>

                        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
                            <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <div className="sm:flex sm:items-start">
                                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                                            Add New Data Source
                                        </h3>
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                                Select the type of data source you want to connect to your system.
                                            </p>
                                        </div>
                                        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                                            {connectionTypes.map((connection) => (
                                                <NewConnectionCard
                                                    key={connection.type}
                                                    type={connection.type}
                                                    title={connection.title}
                                                    description={connection.description}
                                                    onClick={() => {
                                                        console.log(`Selected ${connection.type} connection`);
                                                        setShowNewConnectionModal(false);
                                                    }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                <button
                                    type="button"
                                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#198856] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                                    onClick={() => setShowNewConnectionModal(false)}
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </AppDashboardLayout>
    );
}
