import React from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';

// Service Card Component
interface ServiceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, icon, features }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-8 transition-all duration-300 hover:shadow-md">
      <div className="bg-primary/10 rounded-lg w-16 h-16 flex items-center justify-center mb-6 text-primary">
        {icon}
      </div>
      <h3 className="text-2xl font-bold mb-3">{title}</h3>
      <p className="text-gray-600 dark:text-gray-300 mb-6">{description}</p>

      <h4 className="text-lg font-semibold mb-3">Features:</h4>
      <ul className="space-y-2">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};



// FAQ Item Component
interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 py-5">
      <button
        className="flex justify-between items-center w-full text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-xl font-semibold">{question}</h3>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div className={`mt-3 text-gray-600 dark:text-gray-300 overflow-hidden transition-all duration-300 ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
        <p>{answer}</p>
      </div>
    </div>
  );
};

export default function Services() {
  const { auth, seo } = usePage<SharedData & { seo: any }>().props;

  // Services data
  const services = [
    {
      title: "CSV Import & Export",
      description: "Seamlessly import and export CSV files with advanced mapping capabilities.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
      ),
      features: [
        "Drag-and-drop file uploads",
        "Column mapping and validation",
        "Support for multiple file formats",
        "Batch processing capabilities"
      ]
    },
    {
      title: "Data Transformation",
      description: "Transform your CSV data with powerful tools and customizable templates.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      features: [
        "Field mapping and transformation",
        "Data cleansing and normalization",
        "Custom formula creation",
        "Conditional formatting"
      ]
    },
    {
      title: "Template Management",
      description: "Create and save templates for frequently used transformations.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      features: [
        "Save and reuse transformation settings",
        "Template sharing across team members",
        "Version control for templates",
        "Template categories and tags"
      ]
    }
  ];


  // FAQ data
  const faqs = [
    {
      question: "What file formats do you support?",
      answer: "We primarily support CSV files, but we also support Excel (.xlsx, .xls), TSV, and other delimited text files. Our system automatically detects the file format and handles the import accordingly."
    },
    {
      question: "How secure is my data?",
      answer: "Your data security is our top priority. We use industry-standard encryption for all data transfers and storage. Your files are processed in isolated environments and are automatically deleted after processing unless you choose to save them in your account."
    },
    {
      question: "Can I try before I buy?",
      answer: "Yes! We offer a 14-day free trial of our Professional plan with no credit card required. This gives you full access to all features so you can thoroughly test our service with your own data."
    },
    {
      question: "Do you offer custom solutions?",
      answer: "Absolutely. Our Enterprise plan includes custom transformation options, and we can work with your team to develop specific solutions for your unique data challenges. Contact our sales team to discuss your requirements."
    },
    {
      question: "How do I get support if I have questions?",
      answer: "We offer multiple support channels including email support, an extensive knowledge base, and video tutorials. Professional and Enterprise plans include priority support with faster response times."
    }
  ];

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif">Our Services</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Comprehensive CSV transformation solutions to streamline your data workflow.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">What We Offer</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Our comprehensive suite of CSV transformation services designed to meet all your data needs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <ServiceCard
                  key={index}
                  title={service.title}
                  description={service.description}
                  icon={service.icon}
                  features={service.features}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800 relative overflow-hidden">
          {/* Background decorations */}
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-32 h-32 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl"></div>
            <div className="absolute bottom-20 right-10 w-40 h-40 bg-primary/5 dark:bg-primary/10 rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-primary/3 dark:bg-primary/5 rounded-full blur-3xl"></div>
          </div>

          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 relative z-10">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-primary/10 dark:bg-primary/20 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Simple Process
              </div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-gray-900 dark:text-white leading-tight">
                How It <span className="text-primary">Works</span>
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Our simple three-step process makes CSV transformation quick and easy.
                <span className="font-semibold text-gray-800 dark:text-white">Get started in minutes</span>, not hours.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
              {/* Step 1 */}
              <div className="relative group">
                <div className="bg-white dark:bg-[#1e2938] rounded-2xl p-8 shadow-lg dark:shadow-2xl border border-gray-200 dark:border-gray-600 hover:shadow-xl dark:hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 text-center relative overflow-hidden">
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 dark:bg-primary/10 rounded-full -translate-y-10 translate-x-10"></div>

                  {/* Step number with enhanced design */}
                  <div className="relative mb-6">
                    <div className="inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary to-primary/80 text-white text-2xl font-bold shadow-lg relative z-10">
                      1
                    </div>
                    <div className="absolute inset-0 dark:bg-primary/30 rounded-2xl blur-lg transform scale-110"></div>
                  </div>

                  {/* Icon */}
                  <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Upload Your CSV</h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Simply drag and drop your CSV file or select it from your computer.
                    <span className="font-medium text-gray-800 dark:text-white">Supports files up to 100MB</span>.
                  </p>

                  {/* Features list */}
                  <div className="mt-6 space-y-2">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Drag & drop support
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Instant validation
                    </div>
                  </div>
                </div>

                {/* Connection line */}
                <div className="hidden md:block absolute top-1/2 -right-6 w-12 h-0.5 bg-gradient-to-r from-primary/50 to-primary/20 dark:from-primary/40 dark:to-primary/10 transform -translate-y-1/2 z-20"></div>
              </div>

              {/* Step 2 */}
              <div className="relative group">
                <div className="bg-white dark:bg-[#1e2938] rounded-2xl p-8 shadow-lg dark:shadow-2xl border border-gray-200 dark:border-gray-600 hover:shadow-xl dark:hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 text-center relative overflow-hidden">
                  {/* Background decoration */}
                  <div className="absolute bottom-0 left-0 w-16 h-16 bg-primary/5 dark:bg-primary/10 rounded-full translate-y-8 -translate-x-8"></div>

                  {/* Step number with enhanced design */}
                  <div className="relative mb-6">
                    <div className="inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary to-primary/80 text-white text-2xl font-bold shadow-lg relative z-10">
                      2
                    </div>
                    <div className="absolute inset-0 dark:bg-primary/30 rounded-2xl blur-lg transform scale-110"></div>
                  </div>

                  {/* Icon */}
                  <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Configure Transformations</h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Set up your desired transformations using our intuitive interface.
                    <span className="font-medium text-gray-800 dark:text-white">No coding required</span>.
                  </p>

                  {/* Features list */}
                  <div className="mt-6 space-y-2">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Visual interface
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Real-time preview
                    </div>
                  </div>
                </div>

                {/* Connection line */}
                <div className="hidden md:block absolute top-1/2 -right-6 w-12 h-0.5 bg-gradient-to-r from-primary/50 to-primary/20 dark:from-primary/40 dark:to-primary/10 transform -translate-y-1/2 z-20"></div>
              </div>

              {/* Step 3 */}
              <div className="relative group">
                <div className="bg-white dark:bg-[#1e2938] rounded-2xl p-8 shadow-lg dark:shadow-2xl border border-gray-200 dark:border-gray-600 hover:shadow-xl dark:hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 text-center relative overflow-hidden">
                  {/* Background decoration */}
                  <div className="absolute top-0 left-0 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full -translate-y-12 -translate-x-12"></div>

                  {/* Step number with enhanced design */}
                  <div className="relative mb-6">
                    <div className="inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary to-primary/80 text-white text-2xl font-bold shadow-lg relative z-10">
                      3
                    </div>
                    <div className="absolute inset-0 dark:bg-primary/30 rounded-2xl blur-lg transform scale-110"></div>
                  </div>

                  {/* Icon */}
                  <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Download Results</h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Process your data and download the transformed CSV file instantly.
                    <span className="font-medium text-gray-800 dark:text-white">Lightning fast processing</span>.
                  </p>

                  {/* Features list */}
                  <div className="mt-6 space-y-2">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Instant download
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Multiple formats
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom CTA */}
            <div className="text-center mt-16">
              <div className="inline-flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-6 me-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Average processing time: <span className="font-semibold text-gray-800 dark:text-white">Under 30 seconds </span>
              </div>
              <a
                href="#"
                className="inline-flex items-center gap-3 bg-primary text-white font-semibold rounded-xl px-8 py-4 text-lg hover:bg-primary/90 hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Try It Now - It's Free
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </a>
            </div>
          </div>
        </section>


        {/* FAQ Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Frequently Asked Questions</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Find answers to common questions about our services.
              </p>
            </div>

            <div className="max-w-3xl mx-auto">
              {faqs.map((faq, index) => (
                <FAQItem
                  key={index}
                  question={faq.question}
                  answer={faq.answer}
                />
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Ready to Transform Your CSV Data?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Start your 14-day free trial today. No credit card required.
            </p>
            <a
              href="#"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Start Free Trial
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
