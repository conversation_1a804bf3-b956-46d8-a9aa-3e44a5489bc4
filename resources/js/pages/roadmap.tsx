import React from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/react';

// Roadmap Item Component
interface RoadmapItemProps {
  title: string;
  description: string;
  status: 'planned' | 'in-progress' | 'completed' | 'coming-soon';
  quarter: string;
  votes: number;
  onVote: () => void;
}

const RoadmapItem: React.FC<RoadmapItemProps> = ({ 
  title, 
  description, 
  status, 
  quarter,
  votes,
  onVote
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'planned':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'coming-soon':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'planned':
        return 'Planned';
      case 'in-progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'coming-soon':
        return 'Coming Soon';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="flex justify-between items-start mb-4">
        <span className={`inline-block text-xs font-medium px-2 py-1 rounded-full ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {quarter}
        </span>
      </div>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {description}
      </p>
      <div className="flex justify-between items-center">
        <button 
          onClick={onVote}
          className="flex items-center space-x-1 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
          </svg>
          <span>{votes} votes</span>
        </button>
        {status === 'completed' && (
          <span className="flex items-center text-green-600 dark:text-green-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Shipped
          </span>
        )}
      </div>
    </div>
  );
};

// Feature Request Form Component
const FeatureRequestForm: React.FC = () => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
      <h3 className="text-xl font-bold mb-4">Request a Feature</h3>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Have a feature in mind that would make Bulkify Connect even better? Let us know!
      </p>
      <form className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Your Name
          </label>
          <input
            type="text"
            id="name"
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-900 dark:text-white"
            required
          />
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Your Email
          </label>
          <input
            type="email"
            id="email"
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-900 dark:text-white"
            required
          />
        </div>
        <div>
          <label htmlFor="feature" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Feature Description
          </label>
          <textarea
            id="feature"
            rows={4}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-900 dark:text-white"
            required
          ></textarea>
        </div>
        <div>
          <label htmlFor="useCase" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Use Case
          </label>
          <textarea
            id="useCase"
            rows={2}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-900 dark:text-white"
            placeholder="How would this feature help you?"
            required
          ></textarea>
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-200"
        >
          Submit Request
        </button>
      </form>
    </div>
  );
};

export default function Roadmap() {
  const { auth } = usePage<SharedData>().props;
  const [roadmapItems, setRoadmapItems] = React.useState([
    {
      title: "Advanced CSV Validation Rules",
      description: "Create custom validation rules for your CSV data with conditional logic and data type checking.",
      status: 'completed' as const,
      quarter: 'Q1 2023',
      votes: 156
    },
    {
      title: "Bulk Template Management",
      description: "Organize and manage multiple templates with folders, tags, and batch operations.",
      status: 'completed' as const,
      quarter: 'Q1 2023',
      votes: 142
    },
    {
      title: "API Integration Enhancements",
      description: "Expanded API capabilities for seamless integration with third-party services and applications.",
      status: 'in-progress' as const,
      quarter: 'Q2 2023',
      votes: 189
    },
    {
      title: "Custom Formula Builder",
      description: "Create complex formulas for data transformation with an intuitive drag-and-drop interface.",
      status: 'in-progress' as const,
      quarter: 'Q2 2023',
      votes: 201
    },
    {
      title: "Collaborative Workspace",
      description: "Work together with your team on CSV transformations with real-time collaboration features.",
      status: 'planned' as const,
      quarter: 'Q3 2023',
      votes: 178
    },
    {
      title: "Advanced Data Visualization",
      description: "Visualize your CSV data with interactive charts and graphs to gain insights before and after transformation.",
      status: 'planned' as const,
      quarter: 'Q3 2023',
      votes: 165
    },
    {
      title: "AI-Powered Data Cleansing",
      description: "Automatically detect and fix common data issues using machine learning algorithms.",
      status: 'coming-soon' as const,
      quarter: 'Q4 2023',
      votes: 234
    },
    {
      title: "Scheduled Transformations",
      description: "Set up recurring transformation jobs to process CSV files automatically on a schedule.",
      status: 'coming-soon' as const,
      quarter: 'Q4 2023',
      votes: 198
    }
  ]);

  const handleVote = (index: number) => {
    const newItems = [...roadmapItems];
    newItems[index].votes += 1;
    setRoadmapItems(newItems);
  };

  // Group items by status
  const completedItems = roadmapItems.filter(item => item.status === 'completed');
  const inProgressItems = roadmapItems.filter(item => item.status === 'in-progress');
  const plannedItems = roadmapItems.filter(item => item.status === 'planned');
  const comingSoonItems = roadmapItems.filter(item => item.status === 'coming-soon');

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Head title="Product Roadmap" />
        
        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif">Product Roadmap</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              See what we're working on and what's coming next for Bulkify Connect. Vote for features you'd like to see prioritized.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Status Overview Section */}
        <section className="py-12 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold mb-1">Completed</h3>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">{completedItems.length}</p>
              </div>
              
              <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold mb-1">In Progress</h3>
                <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{inProgressItems.length}</p>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold mb-1">Planned</h3>
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{plannedItems.length}</p>
              </div>
              
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold mb-1">Coming Soon</h3>
                <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{comingSoonItems.length}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Roadmap Content Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Main Content */}
              <div className="w-full lg:w-2/3">
                {/* In Progress Section */}
                <div className="mb-16">
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 font-serif flex items-center">
                    <span className="inline-block w-8 h-8 rounded-full bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </span>
                    In Progress
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {inProgressItems.map((item, index) => (
                      <RoadmapItem 
                        key={index}
                        title={item.title}
                        description={item.description}
                        status={item.status}
                        quarter={item.quarter}
                        votes={item.votes}
                        onVote={() => handleVote(roadmapItems.indexOf(item))}
                      />
                    ))}
                  </div>
                </div>
                
                {/* Planned Section */}
                <div className="mb-16">
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 font-serif flex items-center">
                    <span className="inline-block w-8 h-8 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </span>
                    Planned
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {plannedItems.map((item, index) => (
                      <RoadmapItem 
                        key={index}
                        title={item.title}
                        description={item.description}
                        status={item.status}
                        quarter={item.quarter}
                        votes={item.votes}
                        onVote={() => handleVote(roadmapItems.indexOf(item))}
                      />
                    ))}
                  </div>
                </div>
                
                {/* Coming Soon Section */}
                <div className="mb-16">
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 font-serif flex items-center">
                    <span className="inline-block w-8 h-8 rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </span>
                    Coming Soon
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {comingSoonItems.map((item, index) => (
                      <RoadmapItem 
                        key={index}
                        title={item.title}
                        description={item.description}
                        status={item.status}
                        quarter={item.quarter}
                        votes={item.votes}
                        onVote={() => handleVote(roadmapItems.indexOf(item))}
                      />
                    ))}
                  </div>
                </div>
                
                {/* Completed Section */}
                <div>
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 font-serif flex items-center">
                    <span className="inline-block w-8 h-8 rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    Completed
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {completedItems.map((item, index) => (
                      <RoadmapItem 
                        key={index}
                        title={item.title}
                        description={item.description}
                        status={item.status}
                        quarter={item.quarter}
                        votes={item.votes}
                        onVote={() => handleVote(roadmapItems.indexOf(item))}
                      />
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Sidebar */}
              <div className="w-full lg:w-1/3">
                <div className="sticky top-8">
                  <FeatureRequestForm />
                  
                  <div className="bg-primary/10 rounded-lg p-6 mt-8">
                    <h3 className="text-xl font-bold mb-4">How We Prioritize</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      We consider several factors when prioritizing our roadmap:
                    </p>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Customer votes and feedback</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Strategic alignment with our vision</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Technical feasibility and dependencies</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Potential impact on user experience</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mt-8 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-xl font-bold mb-4">Stay Updated</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      Subscribe to our product updates to be notified when new features are released.
                    </p>
                    <form className="space-y-3">
                      <input
                        type="email"
                        placeholder="Your email address"
                        className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-900 dark:text-white"
                        required
                      />
                      <button
                        type="submit"
                        className="w-full px-4 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-200"
                      >
                        Subscribe
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Have a Feature Idea?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              We're always looking for ways to improve Bulkify Connect. Share your ideas and help shape the future of our product.
            </p>
            <a 
              href="#feature-request"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Request a Feature
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
