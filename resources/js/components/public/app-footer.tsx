import React from 'react';
import { Link } from '@inertiajs/react';
import AppLogo from '@/components/app-logo';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Github, ArrowRight } from 'lucide-react';

interface FooterLinkGroupProps {
  title: string;
  links: Array<{
    text: string;
    href: string;
  }>;
}

const FooterLinkGroup: React.FC<FooterLinkGroupProps> = ({ title, links }) => {
  return (
    <div className="w-full sm:w-1/2 md:w-1/4 px-4 mb-8">
      <h5 className="font-bold mb-4 text-lg text-gray-900 dark:text-white transition-colors duration-200">{title}</h5>
      <ul className="space-y-2">
        {links.map((link, index) => (
          <li key={index}>
            <Link
              href={link.href}
              className="text-gray-600 hover:text-primary transition-colors duration-200 dark:text-gray-400 dark:hover:text-gray-300"
            >
              {link.text}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

const AppFooter: React.FC = () => {
  const productLinks = [
    { text: 'Features', href: route('home') },
    { text: 'Pricing', href: route('pricing') },
    { text: 'Roadmap', href: route('roadmap') },
  ];

  const companyLinks = [
    { text: 'About', href: route('about') },
    { text: 'Blog', href: route('blog') },
    { text: 'Contact', href: route('contact') },
  ];

  const legalLinks = [
    { text: 'Privacy Policy', href: route('privacy.policy') },
    { text: 'Terms of Service', href: route('terms.service') },
    { text: 'Security', href: '#' },
  ];

  return (
    <footer className="bg-white border-t border-gray-200 py-10 dark:bg-gray-900 dark:border-gray-800 transition-colors duration-200">
      <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
        <div className="flex flex-wrap -mx-4">
          {/* Logo and Description */}
          <div className="w-full sm:w-1/2 md:w-1/4 px-4 mb-8">
            <div className="flex items-center">
              <AppLogo />
            </div>
            <p className="text-gray-600 mt-4 dark:text-gray-400">
              Transform your CSV files with ease.
            </p>
          </div>

          {/* Link Groups */}
          <FooterLinkGroup title="Product" links={productLinks} />
          <FooterLinkGroup title="Company" links={companyLinks} />
          <FooterLinkGroup title="Legal" links={legalLinks} />
        </div>

        <div className="border-t border-gray-200 dark:border-gray-800 mt-8 pt-8">
          <p className="text-center text-gray-600 dark:text-gray-400">
            © {new Date().getFullYear()} Bulkify Connect. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default AppFooter;
