import React from 'react';

interface BenefitItemProps {
  title: string;
  description: string;
}

const BenefitItem: React.FC<BenefitItemProps> = ({ title, description }) => {
  return (
    <div className="flex items-start mb-6">
      <div className="flex-shrink-0 mr-4">
        <div className="flex items-center justify-center bg-primary/20 dark:bg-primary/30 rounded-lg w-10 h-10 text-primary dark:text-primary transition-colors duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      </div>
      <div className="flex-grow">
        <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">{title}</h3>
        <p className="text-gray-600 dark:text-gray-300">{description}</p>
      </div>
    </div>
  );
};

const BenefitsSection: React.FC = () => {
  const benefits = [
    {
      title: "Save Time",
      description: "Automate repetitive CSV transformations and save hours of manual work."
    },
    {
      title: "Reduce Errors",
      description: "Eliminate manual data entry errors with automated transformations."
    },
    {
      title: "Streamline Workflow",
      description: "Create and save templates for frequently used transformations."
    }
  ];

  return (
    <section className="py-12 md:py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-background dark:to-[#2a2839] transition-colors duration-200">
      <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Left Column - Image */}
          <div className="w-full lg:w-1/2">
            <img
              src="/images/benefits-illustration.svg"
              alt="Benefits Illustration"
              className="w-full h-auto rounded-lg"
            />
          </div>

          {/* Right Column - Content */}
          <div className="w-full lg:w-1/2">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 font-serif text-gray-900 dark:text-white transition-colors duration-200">Benefits</h2>

            <div className="space-y-6">
              {benefits.map((benefit, index) => (
                <BenefitItem
                  key={index}
                  title={benefit.title}
                  description={benefit.description}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
