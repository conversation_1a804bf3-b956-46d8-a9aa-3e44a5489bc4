import React, { useState, useEffect } from 'react';
import { Formula, FormulaType, CSVColumn } from '@/types/csv';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import axios from 'axios';
import { toast } from 'sonner';

interface FormulaEditorProps {
  isOpen: boolean;
  onClose: () => void;
  column: CSVColumn | null;
  allColumns: CSVColumn[];
  onSave: (formula: Formula) => void;
  editingFormula?: Formula | null;
}

const FormulaEditor: React.FC<FormulaEditorProps> = ({ 
  isOpen, 
  onClose, 
  column, 
  allColumns,
  onSave,
  editingFormula
}) => {
  const [formulaDefinitions, setFormulaDefinitions] = useState<Record<string, any>>({});
  const [selectedType, setSelectedType] = useState<FormulaType>('none');
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    // Load formula definitions
    const loadDefinitions = async () => {
      try {
        const response = await axios.get(route('csv.formulas.definitions'));
        if (response.data.success) {
          setFormulaDefinitions(response.data.definitions);
        }
      } catch (error) {
        console.error('Failed to load formula definitions:', error);
        toast.error('Failed to load formula definitions');
      }
    };
    
    loadDefinitions();
  }, []);
  
  useEffect(() => {
    // If editing an existing formula, load its values
    if (editingFormula) {
      setSelectedType(editingFormula.type);
      setParameters(editingFormula.parameters);
    } else {
      // Reset values for new formula
      setSelectedType('none');
      setParameters({});
    }
  }, [editingFormula, isOpen]);
  
  const handleSave = () => {
    if (selectedType === 'none') {
      toast.error('Please select a formula type');
      return;
    }
    
    if (!column) return;
    
    const definition = formulaDefinitions[selectedType];
    
    // Validate required parameters
    const missingParams = definition.parameterDefinitions
      .filter(param => param.required && !parameters[param.name])
      .map(param => param.name);
    
    if (missingParams.length > 0) {
      toast.error(`Missing required parameters: ${missingParams.join(', ')}`);
      return;
    }
    
    setIsLoading(true);
    
    const formula: Formula = {
      id: editingFormula ? editingFormula.id : `formula-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: selectedType,
      name: definition.name,
      description: definition.description,
      parameters
    };
    
    // Save the formula
    onSave(formula);
    setIsLoading(false);
    onClose();
  };
  
  if (!column || Object.keys(formulaDefinitions).length === 0) {
    return null;
  }
  
  const definition = formulaDefinitions[selectedType] || { parameterDefinitions: [] };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {editingFormula ? 'Edit Formula' : 'Add Formula to Column'}
          </DialogTitle>
          <DialogDescription>
            {editingFormula 
              ? `Edit formula for column "${column.originalName}"`
              : `Add a new formula to transform the values in "${column.originalName}"`
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label className="text-right text-sm font-medium">Formula Type</label>
            <Select 
              value={selectedType} 
              onValueChange={(value) => {
                setSelectedType(value as FormulaType);
                setParameters({});
              }}
              disabled={isLoading}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select formula" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(formulaDefinitions).filter(def => def.type !== 'none').map((def) => (
                  <SelectItem key={def.type} value={def.type}>
                    {def.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {selectedType !== 'none' && (
            <div className="text-sm text-muted-foreground p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
              {definition.description}
            </div>
          )}
          
          {definition.parameterDefinitions.map((param) => (
            <div key={param.name} className="grid grid-cols-4 items-center gap-4">
              <label htmlFor={param.name} className="text-right text-sm font-medium">
                {param.name}
                {param.required && <span className="text-destructive ml-1">*</span>}
              </label>
              {param.type === 'column' ? (
                <Select 
                  value={parameters[param.name] || ''} 
                  onValueChange={(value) => setParameters({ ...parameters, [param.name]: value })}
                  disabled={isLoading}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select column" />
                  </SelectTrigger>
                  <SelectContent>
                    {allColumns.map((col) => (
                      <SelectItem key={col.id} value={col.id}>
                        {col.originalName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  id={param.name}
                  className="col-span-3"
                  type={param.type === 'number' ? 'number' : 'text'}
                  value={parameters[param.name] !== undefined ? parameters[param.name] : (param.default || '')}
                  onChange={(e) => {
                    const value = param.type === 'number' ? Number(e.target.value) : e.target.value;
                    setParameters({ ...parameters, [param.name]: value });
                  }}
                  placeholder={param.description}
                  disabled={isLoading}
                />
              )}
            </div>
          ))}
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="mr-2">
                  <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              editingFormula ? 'Update Formula' : 'Add Formula'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FormulaEditor;
