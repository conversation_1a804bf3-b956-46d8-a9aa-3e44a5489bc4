import React, { useState } from 'react';
import { useAppearance } from '@/hooks/use-appearance';
import { Moon, Sun } from 'lucide-react';

export default function FloatingThemeToggle() {
    const { appearance, updateAppearance } = useAppearance();
    const [showTooltip, setShowTooltip] = useState(false);

    const toggleTheme = () => {
        updateAppearance(appearance === 'dark' ? 'light' : 'dark');
    };

    return (
        <div className="fixed bottom-6 right-6 md:bottom-8 md:right-8 z-50 group">
            <div className="relative">
                {showTooltip && (
                    <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-white dark:bg-[#2a2839] text-gray-800 dark:text-white text-sm rounded-md shadow-lg border border-gray-200 dark:border-gray-700 whitespace-nowrap">
                        {appearance === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
                        <div className="absolute bottom-0 right-4 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-[#2a2839] border-r border-b border-gray-200 dark:border-gray-700"></div>
                    </div>
                )}
                <button
                    onClick={toggleTheme}
                    onMouseEnter={() => setShowTooltip(true)}
                    onMouseLeave={() => setShowTooltip(false)}
                    className="flex items-center justify-center w-12 h-12 rounded-full bg-white dark:bg-[#2a2839] text-gray-800 dark:text-white shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-[#198856]/50 hover:scale-110 active:scale-95"
                    aria-label={appearance === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
                >
                    {appearance === 'dark' ? (
                        <Sun className="h-6 w-6" />
                    ) : (
                        <Moon className="h-6 w-6" />
                    )}
                </button>
            </div>
        </div>
    );
}
